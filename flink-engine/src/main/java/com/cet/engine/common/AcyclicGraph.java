package com.cet.engine.common;

import java.util.*;

public class AcyclicGraph {
    private Map<String, Edge> nodes;

    public AcyclicGraph() {
        this.nodes = new HashMap<>();
    }

    public Edge getOrCreateNode(String name) {
        return nodes.computeIfAbsent(name, Edge::new);
    }

    public void addEdge(String from, String to) {
        Edge fromNode = getOrCreateNode(from);
        Edge toNode = getOrCreateNode(to);
        fromNode.addNeighbor(toNode);
    }

    static class Edge {
        String name;
        List<Edge> neighbors;

        public Edge(String name) {
            this.name = name;
            this.neighbors = new ArrayList<>();
        }

        public void addNeighbor(Edge neighbor) {
            this.neighbors.add(neighbor);
        }
    }

    public List<String> sortBFS() {
        Map<Edge, Integer> inDegree = new HashMap<>();
        for (Edge node : nodes.values()) {
            inDegree.put(node, 0);
        }

        // 计算每个节点的入度
        for (Edge node : nodes.values()) {
            for (Edge neighbor : node.neighbors) {
                inDegree.put(neighbor, inDegree.get(neighbor) + 1);
            }
        }

        Queue<Edge> queue = new LinkedList<>();
        // 将入度为 0 的节点加入队列
        for (Map.Entry<Edge, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.offer(entry.getKey());
            }
        }

        List<String> sortedList = new ArrayList<>();
        while (!queue.isEmpty()) {
            Edge current = queue.poll();
            sortedList.add(current.name);

            // 减少邻接节点的入度
            for (Edge neighbor : current.neighbors) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                // 如果入度为 0，则加入队列
                if (inDegree.get(neighbor) == 0) {
                    queue.offer(neighbor);
                }
            }
        }

        // 检查是否所有节点都已排序
        if (sortedList.size() != nodes.size()) {
            throw new IllegalArgumentException("The graph is not a Directed Acyclic Graph (DAG).");
        }

        return sortedList;
    }
}
