package com.cet.engine.plugin.base.flink.transform;


import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamTransform;
import com.cet.engine.flink.util.TableUtil;
import com.cet.engine.utils.CheckConfigUtil;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

import java.util.Map;

public class Sql implements FlinkStreamTransform<Row, Row> {

    private String sql;

    private JSONObject config;

    private static final String SQL = Const.SQL;

    @Override
    public DataStream<Row> processStream(FlinkEnvironment env, Map<String,DataStream<Row>> dataStream) {
//        dataStream.print("sql 过滤前 ->");
        StreamTableEnvironment tableEnvironment = env.getStreamTableEnvironment();

        String sql1 = sql.replaceAll("\\{" +SOURCE_TABLE_NAME+ "}", config.getString(SOURCE_TABLE_NAME));
        Table table = tableEnvironment.sqlQuery(sql1);
        return TableUtil.tableToDataStream(tableEnvironment, table, true);
    }

    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return config;
    }


    @Override
    public CheckResult checkConfig() {
       return CheckConfigUtil.check(config,SQL);
    }

    @Override
    public void prepare(FlinkEnvironment env) {
        sql = config.getString(SQL);
    }
}
