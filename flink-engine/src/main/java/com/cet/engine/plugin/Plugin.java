package com.cet.engine.plugin;


import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;

import java.io.Serializable;

public interface Plugin<T> extends Serializable {
    String SOURCE_TABLE_NAME = Const.SOURCE_TABLE_NAME;
    String RESULT_TABLE_NAME = Const.RESULT_TABLE_NAME;

    void setConfig(JSONObject config);

    JSONObject getConfig();

    CheckResult checkConfig();

    void prepare(T prepareEnv);

}
