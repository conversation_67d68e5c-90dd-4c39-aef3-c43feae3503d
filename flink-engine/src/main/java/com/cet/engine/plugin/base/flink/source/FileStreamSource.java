package com.cet.engine.plugin.base.flink.source;

import com.alibaba.fastjson.JSONObject;

import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamSource;
import com.cet.engine.flink.util.SchemaUtil;
import com.cet.engine.utils.CheckConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.io.InputFormat;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.io.RowCsvInputFormat;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.core.fs.Path;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.types.Row;

import java.util.List;
import java.util.Map;


public class FileStreamSource implements FlinkStreamSource<Row> {

    private JSONObject config;

    private InputFormat inputFormat;

    private final static String PATH = "path";
    private final static String SOURCE_FORMAT = "format.type";
    private final static String SCHEMA = "fieldsArray";


    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return config;
    }

    @Override
    public CheckResult checkConfig() {
        return CheckConfigUtil.check(config,PATH,SOURCE_FORMAT,SCHEMA);
    }

    @Override
    public void prepare(FlinkEnvironment env) {
        String path = config.getString(PATH);
        String format = config.getString(SOURCE_FORMAT);
        String schemaContent = config.getString(SCHEMA);
        Path filePath = new Path(path);
        switch (format) {
            case "json":
                Object jsonSchemaInfo = JSONObject.parse(schemaContent);
                RowTypeInfo jsonInfo = SchemaUtil.getTypeInformation((JSONObject) jsonSchemaInfo);
                JsonRowInputFormat jsonInputFormat = new JsonRowInputFormat(filePath, null, jsonInfo);
                inputFormat = jsonInputFormat;
                break;
            case "orc":
                System.out.println("no support orc");
                break;
            case "csv":
                Object csvSchemaInfo = JSONObject.parse(schemaContent);
                TypeInformation[] csvType = SchemaUtil.getCsvType((List<Map<String, String>>) csvSchemaInfo);
                RowCsvInputFormat rowCsvInputFormat = new RowCsvInputFormat(filePath, csvType, true);
                this.inputFormat = rowCsvInputFormat;
                break;
            case "text":
                TextRowInputFormat textInputFormat = new TextRowInputFormat(filePath);
                inputFormat = textInputFormat;
                break;
            default:
                break;
        }

    }

    @Override
    public Integer getParallelism() {

        // 默认为1,
        return config.getInteger(Const.PARALLELISM) == null ? 1 : config.getInteger(Const.PARALLELISM);
    }

    @Override
    public String getName() {

        return StringUtils.isEmpty(config.getString(Const.ID)) ? config.getString(Const.PLUGIN_NAME) : config.getString(Const.ID);
    }

    @Override
    public DataStream<Row> getStreamData(FlinkEnvironment env)  {
        return env.getStreamExecutionEnvironment().createInput(inputFormat)
                .uid(getName())
                .name(getName())
                .setParallelism(getParallelism());
    }
}
