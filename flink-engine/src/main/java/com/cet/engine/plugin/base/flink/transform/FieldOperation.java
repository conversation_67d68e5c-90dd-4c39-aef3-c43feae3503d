package com.cet.engine.plugin.base.flink.transform;


import com.alibaba.fastjson.JSONObject;
import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamTransform;
import com.cet.engine.flink.util.TableUtil;
import com.cet.engine.utils.CheckConfigUtil;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.table.api.Table;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.types.Row;

import java.util.Map;

public class FieldOperation implements FlinkStreamTransform<Row, Row> {


    private JSONObject config;

    private static String SCRIPT_NAME = Const.SCRIPT;
    private static String TARGET_FIELD_NAME = Const.TARGET_FIELD;
    private static String SCRIPT = null;

    @Override
    public DataStream<Row> processStream(FlinkEnvironment env, Map<String,DataStream<Row>> dataStream) {
    // 校验当前数据流是否只接收一个，超过一个按另外的逻辑处理
        if (dataStream.size() != 1) {
            return null;
        }
        DataStream<Row> stream = dataStream.values().iterator().next();
        StreamTableEnvironment tableEnvironment = env.getStreamTableEnvironment();

        return (DataStream<Row>) process(tableEnvironment, stream, "stream");
    }

    private Object process(TableEnvironment tableEnvironment, Object data, String type) {
        System.out.println("[DEBUG] current stage: " + config.getString(Const.NAME));

        String sql = "select *,(" +config.getString(SCRIPT_NAME)+ ") as `{target_field_name}` from {table_name}"
            .replaceAll("\\{target_field_name}", config.getString(TARGET_FIELD_NAME))
            .replaceAll("\\{table_name}", config.getString(SOURCE_TABLE_NAME));

        Table table = tableEnvironment.sqlQuery(sql);
        return TableUtil.tableToDataStream((StreamTableEnvironment) tableEnvironment, table, false);
    }

    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return config;
    }


    @Override
    public CheckResult checkConfig() {
       return CheckConfigUtil.check(config,SCRIPT_NAME );
    }

    @Override
    public void prepare(FlinkEnvironment env) {
        SCRIPT = config.getString(SCRIPT_NAME);
    }

}
