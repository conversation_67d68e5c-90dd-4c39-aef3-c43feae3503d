package com.cet.engine.plugin.base.flink.source;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import org.apache.flink.api.common.serialization.AbstractDeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.api.java.typeutils.ObjectArrayTypeInfo;
import org.apache.flink.api.java.typeutils.RowTypeInfo;
import org.apache.flink.api.scala.typeutils.Types;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

import static org.apache.flink.table.descriptors.Schema.SCHEMA;

public class Deserialization extends AbstractDeserializationSchema<Row> {
    private static final Logger log = LoggerFactory.getLogger(Deserialization.class);

    private JSONObject config;
    private JSONObject schemaInfo;

    public Deserialization(JSONObject config) {
        this.config = config;
    }

    @Override
    public Row deserialize(byte[] bytes) {
        Row row = Row.withNames();

        //5.反序列化
        try {
            
            row.setField("logtime", 1727712000000L);
            row.setField("deviceId", 1);
            row.setField("logicalId", 1);
            row.setField("dataId", 1);
            row.setField("dataTypeId", 1);
            row.setField("value", 1.0);
        } catch (Exception e) {
            log.error(e.toString());
        }
        return row;
    }

    @Override
    public TypeInformation<Row> getProducedType() {
        String schemaContent = config.getString(SCHEMA);
        schemaInfo = (JSONObject)JSONObject.parse(schemaContent, Feature.OrderedField);
        int size = schemaInfo.size();
        String[] fields = new String[size];
        TypeInformation[] informations = new TypeInformation[size];
        int i = 0;
        for (Map.Entry<String, Object> entry : schemaInfo.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue().toString();
            fields[i] = key;
            informations[i] = getType(value);
            i++;
        }

        return new RowTypeInfo(informations, fields);
    }

    private static TypeInformation getType(String value) {
        TypeInformation result;
        if (value.equals("string")) {
            result = Types.STRING();
        } else if (value.equals("int")) {
            result = Types.INT();
        } else if (value.equals("short")) {
            result = Types.SHORT();
        } else if (value.equals("byte")) {
            result = Types.BYTE();
        } else if (value.equals("char")) {
            result = Types.CHAR();
        } else if (value.equals("float")) {
            result = Types.FLOAT();
        } else if (value.equals("boolean")) {
            result = Types.BOOLEAN();
        } else if (value.equals("long")) {
            result = Types.LONG();
        } else if (value.equals("double")) {
            result = Types.DOUBLE();
        } else if (value.equals("dec")) {
            result = Types.JAVA_BIG_DEC();
        } else {
            System.out.println("值无法解析: "+ value +" 默认为string");
            result = ObjectArrayTypeInfo.getInfoFor(Types.STRING());
        }

        return result;
    }
}
