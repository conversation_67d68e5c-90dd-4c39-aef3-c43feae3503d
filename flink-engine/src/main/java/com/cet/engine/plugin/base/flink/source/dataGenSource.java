package com.cet.engine.plugin.base.flink.source;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;

import com.cet.engine.common.CheckResult;
import com.cet.engine.common.Const;
import com.cet.engine.flink.FlinkEnvironment;
import com.cet.engine.flink.stream.FlinkStreamSource;
import com.cet.engine.flink.util.SchemaUtil;
import com.cet.engine.plugin.base.flink.source.util.DataGenFactory;
import com.cet.engine.utils.CheckConfigUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.functions.source.datagen.DataGeneratorSource;
import org.apache.flink.types.Row;

public class dataGenSource implements FlinkStreamSource<Row> {

    private JSONObject config;

    private JSONObject schemaInfo;

    private static Integer rowsPerSecond;
    private static Long numberOfRows;

    @Override
    public void setConfig(JSONObject config) {
        this.config = config;
    }

    @Override
    public JSONObject getConfig() {
        return config;
    }

    @Override
    public CheckResult checkConfig() {

        CheckResult result = CheckConfigUtil.check(config,RESULT_TABLE_NAME);

        return result;
    }

    @Override
    public void prepare(FlinkEnvironment env) {

        schemaInfo = JSONObject.parseObject(config.getString(Const.FIELDS_ARRAY), Feature.OrderedField);
        rowsPerSecond = config.getInteger("rows-per-second");
        numberOfRows = config.getLong("number-of-rows");
    }

    @Override
    public DataStream<Row> getStreamData(FlinkEnvironment env) {

        TypeInformation<Row> typeInfo = SchemaUtil.getTypeInformation(schemaInfo);

        DataGenFactory dataGenFactory = new DataGenFactory(schemaInfo);
        DataGeneratorSource dataGeneratorSource = new DataGeneratorSource(dataGenFactory, rowsPerSecond, numberOfRows);

        return env.getStreamExecutionEnvironment().addSource(dataGeneratorSource)
                .returns(typeInfo)
                .uid(getName())
                .name(getName())
                .setParallelism(getParallelism());

    }

    @Override
    public Integer getParallelism() {

        // 默认为1,
        return config.getInteger(Const.PARALLELISM) == null ? 1 : config.getInteger(Const.PARALLELISM);
    }

    @Override
    public String getName() {

        return StringUtils.isEmpty(config.getString(Const.ID)) ? config.getString(Const.PLUGIN_NAME) : config.getString(Const.ID);
    }


}
