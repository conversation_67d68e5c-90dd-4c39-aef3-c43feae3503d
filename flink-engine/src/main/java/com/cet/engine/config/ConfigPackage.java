package com.cet.engine.config;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class ConfigPackage {

    String packagePrefix;
    String upperEnginx;
    String sourcePackage;
    String transformPackage;
    String sinkPackage;
    String envPackage;
    String baseSourcePackage ;
    String baseTransformPackage;
    String baseSinkPackage ;
    public ConfigPackage(String engine) {
        packagePrefix = "com.cet.engine.plugin.base." + engine;
        upperEnginx = engine.substring(0,1).toUpperCase() + engine.substring(1);
        sourcePackage = packagePrefix + ".source";
        transformPackage = packagePrefix + ".transform";
        sinkPackage = packagePrefix + ".sink";
        envPackage = packagePrefix + ".env";
        baseSourcePackage = packagePrefix + ".Base" + upperEnginx + "Source";
        baseTransformPackage = packagePrefix + ".Base" + upperEnginx + "Transform";
        baseSinkPackage = packagePrefix + ".Base" + upperEnginx + "Sink";
    }
    public ConfigPackage() {

    }

}
