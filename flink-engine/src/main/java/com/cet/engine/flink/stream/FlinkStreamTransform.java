package com.cet.engine.flink.stream;


import com.cet.engine.flink.BaseFlinkTransform;
import com.cet.engine.flink.FlinkEnvironment;
import org.apache.flink.streaming.api.datastream.DataStream;

import java.sql.SQLException;
import java.util.Map;

public interface FlinkStreamTransform<IN, OUT> extends BaseFlinkTransform {

    DataStream<OUT> processStream(FlinkEnvironment env, Map<String,DataStream<IN>> dataStreams) throws InterruptedException, SQLException;
}
