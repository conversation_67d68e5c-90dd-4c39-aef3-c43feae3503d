package com.cet.engine.enums;

/**
 * @program: calculation-core
 * @description:
 * @author:
 * @create:
 **/
public enum Engine {
    /**
     * spark 引擎
     */
    SPARK("spark"),
    /**
     * flink引擎
     */
    FLINK("flink"),

    /**
     * null引擎
     */
    NULL("");

    private String engine;
    Engine(String engine) {
        this.engine = engine;
    }

    public String getEngine() {
        return engine;
    }
}
