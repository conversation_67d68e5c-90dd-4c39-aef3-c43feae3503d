package com.cet.engine.enums;

import lombok.Getter;

/**
 * @program: calculation-core
 * @description:
 * @author:
 * @create:
 **/
@Getter
public enum PluginType {
    /**
     * 算子, source
     */
    SOURCE("source"),
    /**
     * 算子. transform
     */
    TRANS("trans"),
    /**
     * 算子, sink
     */
    SINK("sink");

    private String type;
    private PluginType(String type) {
        this.type = type;
    }

}
